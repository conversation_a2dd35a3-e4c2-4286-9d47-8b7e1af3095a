{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\order\\search.vue?vue&type=template&id=89cb67ba&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\order\\search.vue", "mtime": 1754441908234}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div class=\"container mt-1\">\n      <el-form v-model=\"searchFrom\" inline size=\"small\">\n        <el-form-item :label=\"$t('order.search.orderNo') + '：'\">\n          <el-input\n            v-model=\"searchFrom.orderNo\"\n            :placeholder=\"$t('order.search.orderNo')\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('order.search.productTitle') + '：'\">\n          <el-input\n            v-model=\"searchFrom.productTitle\"\n            :placeholder=\"$t('order.search.productTitle')\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('order.search.status') + '：'\">\n          <el-select\n            v-model=\"searchFrom.status\"\n            :placeholder=\"$t('common.all')\"\n          >\n            <el-option\n              v-for=\"item in statusList\"\n              :key=\"item.value\"\n              :label=\"$t('order.search.' + item.label)\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"getList(1)\">\n      {{ $t(\"common.query\") }}\n    </el-button>\n\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"resetForm\">\n      {{ $t(\"common.reset\") }}\n    </el-button>\n  </el-card>\n\n  <el-card class=\"box-card\" style=\"margin-top: 12px\">\n    <el-table\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        :label=\"$t('common.serialNumber')\"\n        type=\"index\"\n        width=\"110\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.image')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.orderId')\"\n        min-width=\"80\"\n        prop=\"id\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('chainTransferRecord.nickname')\"\n        min-width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.realName | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.orderNo')\"\n        min-width=\"80\"\n        prop=\"orderId\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.productName')\"\n        width=\"120\"\n        prop=\"productName\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.payCount')\"\n        min-width=\"80\"\n        prop=\"payCount\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.payCount | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.actualCommission')\"\n        width=\"120\"\n        prop=\"price\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.payPrice')\"\n        width=\"120\"\n        prop=\"totalPrice\"\n      ></el-table-column>\n      <el-table-column :label=\"$t('order.search.commissionRate')\" width=\"80\">\n        <template slot-scope=\"scope\">{{\n          formatRate(scope.row.commissionRate)\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.estimatedCommission')\"\n        width=\"140\"\n        prop=\"estimatedCommission\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.userCashBackRate')\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">{{\n          formatRate(scope.row.userCashBackRate)\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.creatTime')\"\n        width=\"120\"\n        prop=\"createTime\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.contentId')\"\n        min-width=\"80\"\n        prop=\"contentId\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('order.search.statusLabel')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">{{\n            $t(`order.search.${scope.row.statusCode}`)\n          }}</template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      class=\"mt20\"\n      @size-change=\"sizeChange\"\n      @current-change=\"pageChange\"\n      :current-page=\"searchFrom.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"searchFrom.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"searchFrom.total\"\n    >\n    </el-pagination>\n  </el-card>\n</div>\n", null]}