{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.474",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.557",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.admin.GencoAdminApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:28.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e3060d8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:30.788",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:30.788",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:30.790",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.040",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.633",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.633",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.651",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.651",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.652",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.652",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.652",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:31.652",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:35.723",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "298 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:36.116",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:36.125",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:36.173",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:36.186",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:37.732",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(1)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:37.740",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(1)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:55:38.462",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(3)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:30.969",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:30.969",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:30.982",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:30.982",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:31.346",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:31.348",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6261fad1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:31.357",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:31.597",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:31.597",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@106215fc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:31.599",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:39.939",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:39.939",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:39.976",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=4588439575f551fca216cf6856b7989b, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.058",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.060",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4d790778]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.063",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.097",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754441801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.098",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754441801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.098",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.098",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.110",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.110",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3623f09a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.115",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.124",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754441801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.124",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.202",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.202",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@68f18c45]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.206",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.338",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.338",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1bf766f2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754441801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754441801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754441801", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.507",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.507",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4771b0fb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.512",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.514",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.514",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@fc7ca20]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.517",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.589",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.590",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@724103ab]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:41.592",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.192",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/user/level/list?temp=1754441807", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.193",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemUserLevelController#getList()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.360",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.361",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4e6635c4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.368",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.707",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&phone=&level=&page=1&limit=20&total=0&temp=1754441807", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:47.708",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:48.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:48.890",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5a5f09d3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:48.897",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:50.535",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/order/list?orderNo=&productTitle=&type=2&dateLimit=&page=1&limit=20&total=0&temp=1754441810", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:50.536",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreOrderController#getList(StoreOrderSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:51.044",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:51.044",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@78edda79]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:51.056",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.635",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=999999&name=&type=-1&temp=1754441814", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.635",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/productShareRecord/list?keyword=&brandCode=&page=1&limit=20&total=0&temp=1754441814", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.636",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.636",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductShareRecordController#getList(StoreProductShareRecordSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.835",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.835",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6311e0ef]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:54.841",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:57.608",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:57.609",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@a8e0bd8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:56:57.613",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:07.292",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&phone=&level=6&page=1&limit=20&total=47&temp=1754441827", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:07.293",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:07.385",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:07.385",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1a58405f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:07.388",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:14.666",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&phone=&level=&page=1&limit=20&total=0&temp=1754441834", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:14.667",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:15.581",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:15.581",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@699ed0eb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:57:15.585",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.407",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754441990", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.408",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.413",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@79b8251]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.415",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754441990", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.511",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.511",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5ef0a862]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.514",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.600",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754441990", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.600",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754441990", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.602",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/user/level/list?temp=1754441990", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.602",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemUserLevelController#getList()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.602",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.602",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.688",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.688",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@71299f4a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.691",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.769",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.770",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@129be020]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@73094b94]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.793",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.990",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&level=&page=1&limit=20&total=0&temp=1754441990", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:50.990",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:51.984",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:51.984",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@62e6c38c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:51.987",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:54.742",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&level=0&page=1&limit=20&total=47&temp=1754441994", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:54.744",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:55.739",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:55.739",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@92f8391]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 08:59:55.741",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:00:00.087",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&level=1&page=1&limit=20&total=41&temp=1754442000", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:00:00.088",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:00:00.641",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:00:00.641",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@429c4d77]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:00:00.642",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:55.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754442175", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:55.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:55.723",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:55.723",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6f244dd3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:55.724",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.058",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754442178", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.058",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442178", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.058",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.058",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.316",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.316",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3b6d53e8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:58.319",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:59.903",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:59.905",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@11d43fbb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:02:59.907",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:09.039",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=NAILA+klamby&type=-1&temp=1754442189", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:09.040",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:09.284",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:09.284",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@713c6d69]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:09.286",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:12.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442192", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:12.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:14.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:14.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3a44a4a1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:14.414",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:15.336",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=0&temp=1754442195", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:15.337",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:15.421",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:15.421",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@56e64eb1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:15.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:20.256",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=1&temp=1754442200", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:20.258",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:22.004",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:22.004",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4eb86831]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:22.009",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:26.275",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442206", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:26.276",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:27.999",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:27.999",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@42951db5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:28.002",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:42.916",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/add", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:42.917",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#addBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:42.923",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=null, code=null, name=test, logoUrl=, description=null, industry=FUZHUANG (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:43.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.genco.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:43.165",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:43.166",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@196e867b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:43.167",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'
### The error may exist in com/genco/service/dao/StoreBrandDao.java (best guess)
### The error may involve com.genco.service.dao.StoreBrandDao.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO eb_store_brand  ( code, name, logo_url,  status,     industry, platform,         contact_person, contact_phone )  VALUES  ( ?, ?, ?,  ?,     ?, ?,         ?, ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'
; Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX']" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:03:43.169",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.497",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/add", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.498",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#addBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.501",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=null, code=null, name=test, logoUrl=, description=null, industry=FUZHUANG (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.659",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.genco.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.662",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@e720bd6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.663",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'
### The error may exist in com/genco/service/dao/StoreBrandDao.java (best guess)
### The error may involve com.genco.service.dao.StoreBrandDao.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO eb_store_brand  ( code, name, logo_url,  status,     industry, platform,         contact_person, contact_phone )  VALUES  ( ?, ?, ?,  ?,     ?, ?,         ?, ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'
; Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX']" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:02.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/add", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.646",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#addBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.648",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=null, code=null, name=test, logoUrl=1, description=null, industry=FUZHUAN (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.genco.common.exception.GlobalExceptionHandler#defaultExceptionHandler(Exception)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.811",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3cbb37b9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'
### The error may exist in com/genco/service/dao/StoreBrandDao.java (best guess)
### The error may involve com.genco.service.dao.StoreBrandDao.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO eb_store_brand  ( code, name, logo_url,  status,     industry, platform,         contact_person, contact_phone )  VALUES  ( ?, ?, ?,  ?,     ?, ?,         ?, ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'
; Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'TEST' for key 'eb_store_brand.eb_store_brand_code_IDX']" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:32.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.462",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/add", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.462",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#addBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.465",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=null, code=null, name=test123, logoUrl=1, description=null, industry=FUZH (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.635",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.636",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@18a07e44]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.641",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.663",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442285", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:45.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:47.382",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:47.382",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@423891c1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:04:47.386",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754442307", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.791",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.794",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.794",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@34c14b9f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.796",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.821",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754442307", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.821",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.907",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.907",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4fe7f445]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:07.909",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.015",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442308", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.015",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754442308", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.015",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754442308", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.015",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.015",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.015",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.017",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754442308", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.018",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.103",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.103",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1fc30078]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2be58881]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.272",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.272",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7209d84e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:08.274",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:10.103",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:10.103",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6d6d524a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:10.106",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:17.444",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442317", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:17.445",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:17.719",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:17.719",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@332ac257]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:17.722",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/batchUpdate", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:24.709",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#batchUpdateBrand(List)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:24.713",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [[StoreBrandUpdateRequest(id=77, code=null, name=null, logoUrl=null, description=null, industry=null, (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.019",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.019",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@715dba91]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.022",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.033",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442325", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.034",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.305",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.305",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4ef942ad]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:25.308",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754442329", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.491",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.495",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.495",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6f47e8ed]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.498",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.579",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754442329", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.579",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.673",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@503fa688]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.676",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442329", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754442329", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754442329", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.776",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754442329", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.871",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.871",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@548a87dc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.873",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.877",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2f000655]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.880",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.938",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.938",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@493de36]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:29.941",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:31.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:31.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3b5f7a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:31.728",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:34.115",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442334", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:34.115",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&brand=&temp=1754442334", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:34.116",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:34.116",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:36.989",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:36.989",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7df4607f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:36.992",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:42.520",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:42.520",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4785baa7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:42.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:44.259",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442344", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:44.260",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:47.188",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:47.188",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@74f3a2ba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:05:47.191",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:00.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442360", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:00.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754442360", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:00.893",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:00.893",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:01.051",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:01.051",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3bcb5231]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:01.053",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:02.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:02.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5af2802]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:02.678",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:08.176",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442368", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:08.177",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:08.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:08.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@380c71a6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:08.427",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.268",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/batchUpdate", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.269",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#batchUpdateBrand(List)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.272",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [[StoreBrandUpdateRequest(id=77, code=null, name=null, logoUrl=null, description=null, industry=null, (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.530",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.530",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6a53361e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.531",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442375", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.787",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.787",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2311febd]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:06:15.789",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:02.068",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442422", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:02.068",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:04.986",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:04.986",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@430e5a58]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:04.988",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:25.035",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&brand=TEST123&temp=1754442445", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:25.035",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:25.132",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:25.132",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@434dca5f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:25.134",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:27.358",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&temp=1754442447", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:27.359",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:36.704",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:36.705",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1a56f8c1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:36.710",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:58.843",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/batchUpdate", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:58.843",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#batchUpdateBrand(List)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:58.847",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [[StoreBrandUpdateRequest(id=77, code=null, name=null, logoUrl=null, description=null, industry=null, (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.092",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.092",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5884092e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.094",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.104",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442479", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.105",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.352",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.352",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@46beef3e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:07:59.354",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:02.293",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442482", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:02.294",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:05.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:05.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3ff55f8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:05.163",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:14.610",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442494", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:14.610",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:17.470",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:17.470",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@ad9f533]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:17.474",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:57.928",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/batchUpdate", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:57.929",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#batchUpdateBrand(List)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:57.931",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [[StoreBrandUpdateRequest(id=77, code=null, name=null, logoUrl=null, description=null, industry=null, (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.177",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.177",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@39f5e3f7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.179",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.193",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442538", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.194",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.275",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.276",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@f529474]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:58.277",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.942",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754442539", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.943",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.946",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.946",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7e333901]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.949",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.975",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754442539", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:08:59.975",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.059",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.059",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@23e53b6e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.061",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754442540", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754442540", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754442540", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442540", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.159",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.160",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.238",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.238",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@394a0ac6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.241",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.243",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.244",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@260b6413]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.245",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3d39659]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:00.439",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:02.389",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:02.389",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4027bad7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:02.392",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:07.430",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=test123&type=-1&temp=1754442547", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:07.430",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:07.528",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:07.528",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5edbda11]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:07.529",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:09.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442549", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:09.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&brand=&temp=1754442549", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:09.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:09.343",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:10.786",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754442550", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:10.787",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:12.483",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:12.483",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5bbf5484]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:12.486",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:14.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:14.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@60b323f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:14.038",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:18.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:18.491",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@55bb00dd]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:18.496",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.154",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754442562", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.154",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754442562", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.155",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.155",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.348",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.348",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@571deae9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:22.351",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:24.131",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:24.132",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@ae885c4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:09:24.133",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754443218", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.214",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.217",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.217",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1ebfb0d8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.221",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.271",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754443218", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.272",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.371",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.371",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7e946824]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.373",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443218", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754443218", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754443218", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.678",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754443218", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.680",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.769",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.770",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2054090b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.771",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.946",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.946",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@f1478a6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:18.948",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:19.212",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:19.212",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7a613c0a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:19.214",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:21.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:21.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@77bcf286]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:21.016",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.831",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754443231", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.831",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.834",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.834",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5a0bb341]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.837",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.907",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754443231", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:31.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.012",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.012",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@25c7b32]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.014",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754443232", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754443232", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.113",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754443232", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443232", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.119",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.208",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@147cfadd]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.210",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.306",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.308",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@dffbbe4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.310",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.385",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.385",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1aeac0e4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:32.386",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:36.577",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:36.577",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7905b7f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:36.579",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.498",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754443240", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.499",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.503",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.503",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@53b8e2a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.506",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.531",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754443240", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.532",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4653c2ce]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.728",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754443240", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754443240", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754443240", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443240", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:40.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.019",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.019",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5fef45ed]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.020",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@184e75ef]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.037",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@400c1284]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:41.192",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:44.785",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:44.785",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@77ce1d1f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:20:44.788",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.883",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754443265", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.884",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.887",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.889",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4f58a7ea]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.891",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.957",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754443265", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:05.957",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.167",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.168",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@51e32542]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.170",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.536",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754443266", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754443266", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.538",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754443266", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.539",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443266", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.626",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.626",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5bcf81e1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.730",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.730",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@17123302]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.732",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.959",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.961",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@43778110]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:06.962",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:08.535",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:08.535",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@490a256f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:08.537",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.504",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754443282", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.505",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.511",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.511",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1268f55b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.514",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.588",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754443282", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.590",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.691",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.692",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@dc1d056]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.694",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.798",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754443282", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.799",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.799",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443282", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.799",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754443282", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.799",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754443282", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.799",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.799",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.800",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.890",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.890",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6eb13988]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.892",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.971",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.971",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@42608635]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:22.973",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:23.004",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:23.004",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2989dfe2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:23.008",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:26.990",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:26.991",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3ed23523]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:26.995",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:37.794",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/add", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:37.795",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#addBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:37.797",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=null, code=null, name=t, logoUrl=1, description=null, industry=, platform (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:38.186",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:38.187",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@37455205]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:38.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443298", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:38.208",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:42.377",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:42.377",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@23e09ad9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:42.379",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:48.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=t&type=-1&temp=1754443308", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:48.014",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:51.640",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:51.640",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@49a5926f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:51.643",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:58.954",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/update", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:58.955",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#updateBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:58.957",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=78, code=T, name=t, logoUrl=1, description=null, industry=, platform=, st (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:59.531",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:59.531",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@c3b4781]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:59.532",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:59.549",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=t&type=-1&temp=1754443319", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:21:59.549",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:03.132",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:03.133",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2e6e1812]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:03.136",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:07.673",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/update", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:07.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#updateBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:07.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=78, code=T, name=t, logoUrl=1, description=null, industry=, platform=, st (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:08.254",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:08.254",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6f8d3f6e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:08.257",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:08.273",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=t&type=-1&temp=1754443328", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:08.274",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:11.861",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:11.861",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@38f7d2ac]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:22:11.864",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:32.172",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=t&type=0&temp=1754443412", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:32.173",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:32.366",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:32.366",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@13713c31]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:32.368",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:34.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=0&temp=1754443414", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:34.084",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:34.276",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:34.276",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4417ae08]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:34.277",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:38.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=2&temp=1754443418", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:38.115",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:38.307",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:38.307",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@78eb8274]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:38.308",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:42.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=1&temp=1754443422", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:42.888",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:47.054",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:47.055",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@34c7cef7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:47.059",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:50.045",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=2&limit=20&name=&type=1&temp=1754443430", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:50.046",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:53.061",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:53.063",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@513b11bf]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:53.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.231",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/update", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.231",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#updateBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.233",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=78, code=T, name=t, logoUrl=1, description=null, industry=, platform=, st (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.806",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.806",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@109b04d4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.830",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=2&limit=20&name=&type=1&temp=1754443437", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:23:57.830",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:00.671",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:00.672",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3174b416]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:00.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/brand/update", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#updateBrand(StoreBrandUpdateRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:08.706",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [StoreBrandUpdateRequest(id=52, code=null, name=测试品牌, logoUrl=2, description=null, industry=1, platfo (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:09.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:09.285",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@60d10835]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:09.288",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:09.306",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=2&limit=20&name=&type=1&temp=1754443449", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:09.308",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:11.957",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:11.957",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@49aaedc9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:11.960",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:14.113",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=0&temp=1754443454", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:14.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:14.683",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:14.685",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7630f31e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:14.687",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:17.478",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=2&temp=1754443457", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:17.478",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:18.047",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:18.047",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5b76a823]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:18.050",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:20.085",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=2&temp=1754443460", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:20.085",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:20.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:20.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6b9d726e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:24:20.658",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.510",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=0a419b62f9f443beb1d81edb14407c7c&temp=1754443639", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.511",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.515",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.515",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6a677b41]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.518",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754443639", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.743",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.743",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@748bdf0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.745",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.845",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754443639", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.845",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754443639", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.845",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754443639", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.846",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754443639", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.846",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.846",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.846",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:19.847",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.037",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.037",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@220a7ef5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.039",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7606ee72]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.104",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.259",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.259",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@18ad8557]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:20.261",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:21.994",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:21.995",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3e9fdec]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:21.997",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:40.611",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754443660", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:40.613",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:40.613",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&brand=&temp=1754443660", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:40.614",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:42.987",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=999999&name=&type=-1&temp=1754443662", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:42.987",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/productShareRecord/list?keyword=&brandCode=&page=1&limit=20&total=0&temp=1754443662", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:42.988",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:42.989",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductShareRecordController#getList(StoreProductShareRecordSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:43.376",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:43.376",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5a7000f9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:43.380",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:43.809",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:43.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4df67ed5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:43.815",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:46.201",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:46.201",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2a5c64e4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:46.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:50.827",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/affiliate/products/search", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:50.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AffiliateProductController#searchProducts(AffiliateProductSearchRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:50.837",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [AffiliateProductSearchRequest(pageSize=5, cursor=, sortField=commission_rate, sortOrder=DESC, titleK (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:56.186",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:56.186",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@ec4eb4f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:27:56.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:01.491",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:01.491",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4ef101c7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:01.494",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:02.816",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/order/list?orderNo=&productTitle=&type=2&dateLimit=&page=1&limit=20&total=0&temp=1754443682", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:02.817",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreOrderController#getList(StoreOrderSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:03.856",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:03.856",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3b1537f8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-06 09:28:03.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
